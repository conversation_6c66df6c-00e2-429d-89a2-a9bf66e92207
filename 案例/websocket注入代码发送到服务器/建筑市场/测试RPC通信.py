#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
建筑市场RPC通信测试脚本
修复了"data is not defined"错误
"""

import requests
import json
import time

def test_building_market_rpc():
    """测试建筑市场RPC通信"""
    
    # 1. 获取建筑市场数据
    print("1. 正在获取建筑市场数据...")
    url = 'https://jzsc.mohurd.gov.cn/APi/webApi/dataservice/query/comp/list'
    
    headers = {
        "Referer": "https://jzsc.mohurd.gov.cn/data/company",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
        "timeout": "30000",
        "v": "231012"
    }
    
    params = {
        'pg': 1,
        'pgsz': 15,
        'total': 450
    }
    
    try:
        response = requests.get(url, headers=headers, params=params, timeout=10)
        response.raise_for_status()
        text = response.text
        print(f"✓ 成功获取数据，长度: {len(text)} 字符")
        print(f"数据预览: {text[:100]}...")
        
    except requests.RequestException as e:
        print(f"✗ 获取数据失败: {e}")
        return False
    
    # 2. 通过RPC调用解密
    print("\n2. 正在通过RPC调用解密...")
    rpc_data = {
        "group": "rpc-test",
        "action": "aaa",
        "data": text
    }
    
    try:
        rpc_response = requests.post(
            "http://127.0.0.1:5620/business-demo/invoke", 
            data=rpc_data,
            timeout=30
        )
        rpc_response.raise_for_status()
        result = rpc_response.text
        print(f"✓ RPC调用成功")
        print(f"响应状态码: {rpc_response.status_code}")
        print(f"响应内容: {result}")
        
        # 尝试解析JSON响应
        try:
            json_result = json.loads(result)
            if json_result.get('status') == 0:
                print("✓ 解密成功!")
                decrypted_data = json_result.get('data', '')
                if decrypted_data:
                    print(f"解密后数据预览: {decrypted_data[:200]}...")
                    return True
            else:
                print(f"✗ 解密失败: {json_result.get('message', '未知错误')}")
                return False
        except json.JSONDecodeError:
            print(f"✗ 响应不是有效的JSON格式: {result}")
            return False
            
    except requests.RequestException as e:
        print(f"✗ RPC调用失败: {e}")
        print("请确保:")
        print("  1. Sekiro服务器正在运行")
        print("  2. 浏览器已打开测试页面并连接到RPC服务器")
        print("  3. CryptoJS库已正确加载")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("建筑市场RPC通信测试")
    print("=" * 50)
    
    print("\n使用说明:")
    print("1. 确保Sekiro服务器正在运行在端口5620")
    print("2. 在浏览器中打开 测试页面.html")
    print("3. 确认页面显示所有组件都已正确加载")
    print("4. 运行此脚本进行测试")
    
    input("\n按回车键开始测试...")
    
    success = test_building_market_rpc()
    
    print("\n" + "=" * 50)
    if success:
        print("✓ 测试完成 - 所有功能正常工作!")
    else:
        print("✗ 测试失败 - 请检查错误信息并修复问题")
    print("=" * 50)

if __name__ == "__main__":
    main()
