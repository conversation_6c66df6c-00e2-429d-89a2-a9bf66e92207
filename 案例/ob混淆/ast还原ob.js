const parse = require('@babel/parser')
const fs = require('fs')
const traverse = require("@babel/traverse").default;
const types = require("@babel/types");
const generator = require("@babel/generator").default;

process.argv.length > 2 ? File1 = process.argv[2] : File1 = './_encode.js'
process.argv.length > 3 ? File2 = process.argv[2] : File2 = './_decode.js'

function _0xda28(){var _0x3c73af=['call','CqHgd','TRDmo','n\x20(fu','liyAh','XjxTF','itwdP','KXaMH','5|4|0','pcBOc','dOuSZ','PsBmJ','UBFiz','ExHgf','Objec','sUvGc','eaFaG','sUhFG','$]*)','wqIzh','ffdar','wHwfe','mPiEr','UNGUq','UJDaV','2jgouUT','bNeLd','BwYvT','toStr','vZkqc','EZydE','MNaBt','JtCdb','fmpHB','dkkiB','searc','YrfoW','DIiIk','WpiMl','QrhFK','XIGQP','njCDa','to__','xzrSj','Mhspa','vRnIr','yxWcJ','qIOka','JFbkd','ROpOZ','state','Qoqrx','BLLva','e)\x20{}','bYajo','iXOuY','terva','log','JdOpE','RswgA','mYmiE','BuErX','gcaxn','abPQg','kcntt','9LFqgsm','3971888ZzritP','\x22retu','DEVqA','\x20(tru','SxInZ','SHyib','sJzYm','533158slTnbb','rFzDe','JyYgX','OLhja','apply','ion\x20*','nnlqp','bDHcC','AOquu','zVVdM','LxPRQ','LTKqK','TQPbZ','gAaCH','IITeP','retur','ThkWH','sjJXJ','bZUoH','kEKmL','nVjUi','{}.co','lengt','dSkuo','ieCqS','pjncX','aWNFp','dMSQN','iZfBH','tchos','CmNEo','sbFBw','WVkmy','APEMy','zviyt','nVhKi','bhjok','ing','warn','VPidI','VAVlR','KFCDU','YyjOx','fYcmm','TvmXw','vNcTe','MjqAY','fbQkc','DkOxH','YyUZn','zgGlR','MBXdi','conso','wdAJq','UGdqH',')+)+)','nctio','info','ctor(','UeDUf','LlcPL','JTwaJ','eWKbL','tion','SIPop','is\x22)(','KhCEe','sUPQJ','actio','*(?:[','proto','xJThq','btoxv','EnRRk','KVgzU','jraGD','CJjJJ','nJYBi','pqfUl','qqWyp','NXruV','PKGSQ','oykAP','suVzv','Blggo','lRVeL','sTZgN','GBKhF','vcvEL','rn\x20th','uTnJt','GSQWV','BEVjT','fdZYX','debu','rhTnM','n()\x20','AzdcO','while','EtoFg','ylvCD','Iydhn','vchNd','yJZvj','FZjpr','pLfSK','GGBRj','funct','kOPDS','msWNa','EPRYa','AlaiO','tWKzC','qNoil','LEKsK','Z_$][','ZMPDi','TvcFM','blqDr','RLdHc','jMeFf','MMofL','mbodq','vliRs','mXLHw','CCXVV','vLmOa','split','MNDNk','error','pHflR','zqplh','FfheW','UtrwL','QlERW','type','setIn','lApzm','CEDoj','eGJwW','oFHzv','rURMS','ziRED','VKDql','rvNmq','1806508sgTABi','ZMaSW','JVaFX','WoiVp','trace','QLUPk','cCBEW','xIOGy','init','fWGuL','JbeDl','count','GTpup','GmIIS','154044OvtcAs','oVQyM','hyWju','Slrzz','eFwAF','cQTFI','IjPOE','DXMSl','PuZBw','aSPWI','\x20Worl','RHAcK','nstru','CqqfE','aOSPs','YNgLp','a-zA-','bBCMx','wDSws','XuwcM','flauy','cIVBq','YIuuD','lyIwt','CDxTB','pMmlE','TxdEK','mTcMU','RvPDI','pnGMV','OCiww','ZEPOq','2774928jIOklv','kAGAs','dOdZd','ZzvDe','OMgiU','excep','const','cgeuY','LvhBC','FSPYw','443548LcPfXu','zNxqi','NzAFA','table','|3|1|','Hello','lynhG','ZDWmp','23631970NOpCms','CpSfE','MPvje','fRvsX','__pro','JeICK','PAXOb','kkdPV','QeEKw','NdLir','puPMU','jPGjS','CcQnj','FxiUT','gger','ructo','0-9a-','PXNGu','jYwmr','jdlqw','aatRg','bind','eLwFB','SnLFa','JcxwM','rrKmS','gawas','SOnVN','|4|1|','ENSlV','axhaz','obnAq','YjBDO','RYUrs','GPWqi','chain','GcbDj','ssRCa','3|2|5','PTFpm','FAHLM','wwrkz','ZymlL','zkkMR','pBNow','XJQSa','bAdOf','QDwFl','cCBdJ','uITuu','input','KheOp','LuIEr','kvhHU','rJQLF','PhiDc','5KfBrTm','ZVvpY','XJrQE','(((.+','cTBIy','SKLYL','test','lChhp','bMOYV','zA-Z_','strin','oYEfS','YQnHY','QWhcw','tzQtG','NSQJl','hKMzb','iZNyS','\x5c+\x5c+\x20','QliBm','fasow','Myfnw','opSsC','UmpZN','SCyol','ybwUy','tbtzK','xSxgY','Oxndd','QfZWF','iusxr','XFHVW','\x5c(\x20*\x5c'];_0xda28=function(){return _0x3c73af};return _0xda28()}

(function(_0x2f6953,_0x4b6862){function _0x527e8f(_0x221a5a,_0x172e68,_0x5873c9,_0x2ef559,_0x877b74){return _0x3f9d(_0x2ef559- -0x2dc,_0x221a5a)}function _0x4b82af(_0x8ffdb4,_0x2dd6b5,_0x5aec17,_0x20e767,_0x46f1c1){return _0x3f9d(_0x20e767-0x22,_0x5aec17)}function _0x4a9575(_0x479d82,_0x5e1adf,_0x235a9f,_0x1a51cf,_0x2da14f){return _0x3f9d(_0x1a51cf-0x27f,_0x5e1adf)}function _0x15d432(_0x5ab8a3,_0x2c1847,_0x3c54fe,_0x126d73,_0x3153be){return _0x3f9d(_0x126d73-0x233,_0x5ab8a3)}function _0x588d0a(_0x1d40d4,_0x3dc7c4,_0x230899,_0xe9719d,_0x201b7f){return _0x3f9d(_0xe9719d- -0x26e,_0x201b7f)}var _0x1edc4d=_0x2f6953();while(!![]){try{var _0x4ca06b=parseInt(_0x4a9575(0x419,0x3e4,0x3cf,0x469,0x477))/(-0x25c2+0x1*-0x20fb+0x46be)*(-parseInt(_0x4a9575(0x2bb,0x361,0x2bc,0x326,0x2d4))/(0x1022+ -0xdcc+ -0x4*0x95))+ -parseInt(_0x588d0a(-0x8d,-0xa4,-0x72,-0x128,-0x17b))/(0x5*0x76f+ -0x120b*-0x2+ -0x493e)+ -parseInt(_0x4a9575(0x3e8,0x3e8,0x3e8,0x3b7,0x427))/(-0x2*-0x1ce+0x9*-0x339+0x1969)+parseInt(_0x527e8f(-0x17c,-0xde,-0xc6,-0x12c,-0xee))/(-0x1f*-0xfb+ -0x20b4+0x254)*(-parseInt(_0x588d0a(-0xeb,-0x4f,-0xba,-0x108,-0x14d))/(-0xfcb*0x1+ -0x1*0x3e5+0x13b6))+ -parseInt(_0x527e8f(-0x1c7,-0x18d,-0x1e6,-0x16c,-0x105))/(-0x1294+ -0x1*-0xf2f+0x36c)+ -parseInt(_0x15d432(0x326,0x378,0x249,0x2d3,0x283))/(-0x3b*0x7d+0x2354+ -0x67d)+parseInt(_0x4a9575(0x2b2,0x393,0x3d0,0x31e,0x266))/(-0x128+ -0x125b+ -0x684*-0x3)*(parseInt(_0x588d0a(-0x142,-0xcf,-0xa0,-0xf6,-0x15d))/(0xb06+ -0x5*0x5fd+0x12f5));if(_0x4ca06b===_0x4b6862)break;else _0x1edc4d['push'](_0x1edc4d['shift']())}catch(_0x5d00fd){_0x1edc4d['push'](_0x1edc4d['shift']())}}}(_0xda28,-0x1*0x882dd+0x1b1e1+0xb7752));


var ast = parse.parse(fs.readFileSync(File1, {encoding: 'utf-8'}));
//收集满足需求的方法
// function _0x579b6e(_0x2d5da7, _0x3f8238, _0x1baccb, _0x261ed5, _0x5e4a5d) {
//     return _0x3f9d(_0x2d5da7 - -0x3b9, _0x3f8238);
// }

// 判断类型是是不是字面量
function isNodeLiteral(node) {
    if (Array.isArray(node)) {
        return node.every(ele => isNodeLiteral(ele));
    }
    if (types.isLiteral(node)) {
        if (node.value == null) {
            return false;
        }
        return true;
    }
    if (types.isBinaryExpression(node)) {
        return isNodeLiteral(node.left) && isNodeLiteral(node.right);
    }
    if (types.isUnaryExpression(node, {
        "operator": "-"
    }) || types.isUnaryExpression(node, {
        "operator": "+"
    })) {
        return isNodeLiteral(node.argument);
    }

    if (types.isObjectExpression(node)) {
        let {properties} = node;
        if (properties.length == 0) {
            return true;
        }
        return properties.every(property => isNodeLiteral(property));
    }
    if (types.isArrayExpression(node)) {
        let {elements} = node;
        if (elements.length == 0) {
            return true;
        }
        return elements.every(element => isNodeLiteral(element));
    }

    return false;
}

var func_code = ''
var func_name = []

traverse(ast,{
    FunctionDeclaration(path){
        var {id,params,body} = path.node;
        if (params.length != 5 || body.body.length != 1){
            return ;
        }
        if (!types.isReturnStatement(body.body[0])){
            return ;
        }
        func_code += path.toString()
        func_name.push(id.name)
    }
})
// console.log(func_code)
// console.log(func_name)
//执行方法  把方法都加载到当前的环境
eval(func_code)
//找到调用的位置  满足需求
traverse(ast,{
    CallExpression(path) {
        var {callee, arguments} = path.node;
        if (!types.isIdentifier(callee) || !func_name.includes(callee.name)){
            return ;
        }
        if (!arguments.length == 5 || !isNodeLiteral(arguments)){
            return ;
        }
        // console.log(path.toString())
        console.log(_0x579b6e(-0x236, -0x260, -0x1f6, -0x2ba, -0x2d2))
        // var value = eval(path.toString())
        // path.replaceWith(types.valueToNode(value))
    }
})

// let {code} = generator(ast);
// console.log(code);
// fs.writeFile(File2, code, (err) => {
// });
